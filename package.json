{"name": "read-box", "author": "Minimals", "version": "6.0.1", "description": "ReadBox: Scan & reserve books from city library shelves", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "tsc && vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "pnpm rm:all && pnpm install && pnpm dev", "re:build": "pnpm rm:all && pnpm install && pnpm build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:host": "vite --host"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/barlow": "^5.0.13", "@fontsource/dm-sans": "^5.0.21", "@fontsource/inter": "^5.0.18", "@fontsource/nunito-sans": "^5.0.13", "@fontsource/public-sans": "^5.0.18", "@hookform/resolvers": "^3.6.0", "@iconify/react": "^5.0.1", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.20", "@mui/x-data-grid": "^7.7.0", "@mui/x-date-pickers": "^7.7.0", "@mui/x-tree-view": "^7.7.0", "@supabase/supabase-js": "^2.50.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.2", "dayjs": "^1.11.11", "framer-motion": "^11.2.10", "history": "^5.3.0", "lodash": "^4.17.21", "nprogress": "^0.2.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.51.5", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "simplebar-react": "^3.2.5", "sonner": "^2.0.4", "stylis": "^4.3.2", "stylis-plugin-rtl": "^2.1.1", "voca": "^1.4.1", "zod": "^3.23.8"}, "devDependencies": {"@types/autosuggest-highlight": "^3.2.3", "@types/lodash": "^4.17.17", "@types/node": "^20.14.2", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/stylis": "^4.2.6", "@types/voca": "^1.4.6", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "@vitejs/plugin-react-swc": "^3.7.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-perfectionist": "^2.11.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unused-imports": "^3.2.0", "prettier": "^3.3.2", "typescript": "^5.4.5", "vite": "^5.3.0", "vite-plugin-checker": "^0.6.4"}}