import type { AuthError } from '@supabase/supabase-js';

import { supabase } from 'src/lib/supabase';

export type GoogleSignInOptions = {
  redirectTo?: string;
  scopes?: string;
  queryParams?: {
    access_type?: 'online' | 'offline';
    prompt?: 'none' | 'consent' | 'select_account';
    hd?: string;
  };
};

/** **************************************
 * Sign in with Google OAuth
 *************************************** */
export const signInWithGoogle = async (options?: GoogleSignInOptions) => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/v1/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
          ...options?.queryParams,
        },
      },
    });

    if (error) {
      console.error('Google sign in error:', error);
      throw error;
    }
    console.log(data, 'data google auth');

    return data;
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};
/** **************************************
 * Sign out
 *************************************** */
export const signOut = async (): Promise<{
  error: AuthError | null;
}> => {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('Sign out error:', error);
      throw error;
    }

    return { error };
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};
