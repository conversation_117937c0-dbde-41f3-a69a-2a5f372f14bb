import React from 'react';
import { Helmet } from 'react-helmet-async';

import { Button, Container } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs/custom-breadcrumbs';

import type { IDashboardPageWrapperProps } from './types';

export const DashboardPageWrapper: React.FC<IDashboardPageWrapperProps> = ({
  pageTitle,
  heading,
  breadcrumbLinks,
  actionButton,
  children,
}) => {
  // Extract layout options with defaults
  const settings = useSettingsContext();

  const defaultActionButton = {
    show: true,
    label: 'Add',
    icon: 'mingcute:add-line',
    buttonProps: {
      variant: 'contained' as const,
      color: 'primary' as const,
    },
  };

  const actionConfig = { ...defaultActionButton, ...actionButton };

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
      </Helmet>

      <Container maxWidth={settings.compactLayout ? 'lg' : false}>
        <CustomBreadcrumbs
          heading={heading}
          links={breadcrumbLinks}
          action={
            actionConfig.show &&
            actionConfig.onClick && (
              <Button
                onClick={actionConfig.onClick}
                startIcon={actionConfig.icon && <Iconify icon={actionConfig.icon} />}
                {...actionConfig.buttonProps}
              >
                {actionConfig.label}
              </Button>
            )
          }
          sx={{
            mb: { xs: 3, md: 5 },
          }}
        />
        {children}
      </Container>
    </>
  );
};
