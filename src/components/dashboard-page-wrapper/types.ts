import type { ButtonProps } from '@mui/material';
import type { IUseBooleanReturn } from 'src/hooks/use-boolean';
import type { BreadcrumbsLinkProps } from 'src/components/custom-breadcrumbs';

export type IDashboardPageWrapperProps = {
  pageTitle: string;
  heading: string;
  breadcrumbLinks: BreadcrumbsLinkProps[];
  actionButton?: {
    show: boolean;
    label: string;
    onClick: IUseBooleanReturn['onTrue'];
    icon?: string;
    buttonProps?: Omit<ButtonProps, 'onClick' | 'children'>;
  };
  children: React.ReactNode;
};
