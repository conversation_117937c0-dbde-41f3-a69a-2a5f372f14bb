import type { StackProps } from '@mui/material/Stack';
import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import { alpha } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { Paper, Button, type ButtonProps } from '@mui/material';

import { Iconify } from '../iconify';

// ----------------------------------------------------------------------

export interface EmptyContentProps extends StackProps {
  // Content Configuration
  title?: string;
  description?: string;
  imgUrl?: string;

  // Action Button Configuration
  actionButton?: {
    label: string;
    onClick: IUseBooleanReturn['onTrue'] | (() => void);
    buttonProps?: Omit<ButtonProps, 'onClick' | 'children'>;
  };
}

export function EmptyContent({
  title,
  imgUrl,
  actionButton,
  description,
  sx,
  ...other
}: EmptyContentProps) {
  return (
    <Paper elevation={3} variant="elevation" sx={{ p: 3 }}>
      <Stack
        flexGrow={1}
        alignItems="center"
        justifyContent="center"
        sx={{
          // px: 3,
          height: 312,
          borderRadius: 1,
          bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04),
          border: (theme) => `dashed 1px ${alpha(theme.palette.grey[500], 0.08)}`,
          p: 3,
          ...sx,
        }}
        {...other}
      >
        <Box
          component="img"
          alt="empty content"
          src={imgUrl || '/assets/icons/empty/ic_content.svg'}
          sx={{ width: 1, maxWidth: 48 }}
        />

        {title && (
          <Typography
            variant="h6"
            component="span"
            sx={{ mt: 1, color: 'text.disabled', textAlign: 'center' }}
          >
            {title}
          </Typography>
        )}

        {description && (
          <Typography variant="caption" sx={{ mt: 1, color: 'text.disabled', textAlign: 'center' }}>
            {description}
          </Typography>
        )}

        {actionButton?.label && (
          <Button
            color="primary"
            variant="contained"
            sx={{ mt: 1 }}
            onClick={actionButton.onClick}
            startIcon={<Iconify icon="mingcute:add-line" />}
            {...actionButton.buttonProps}
          >
            {actionButton.label}
          </Button>
        )}
      </Stack>
    </Paper>
  );
}
