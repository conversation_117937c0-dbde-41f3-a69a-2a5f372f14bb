import type { Theme, SxProps } from '@mui/material/styles';

import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';

import { EmptyContent } from 'src/components/empty-content';

export type TableNoDataProps = {
  show: boolean;
  title: string;
  sx?: SxProps<Theme>;
  subTitle: string | undefined;
};

export function TableNoData({ show, title, subTitle, sx }: TableNoDataProps) {
  return (
    <TableRow>
      {show ? (
        <TableCell colSpan={12}>
          <EmptyContent sx={{ py: 10, ...sx }} />
        </TableCell>
      ) : (
        <TableCell colSpan={12} sx={{ p: 0 }} />
      )}
    </TableRow>
  );
}
