import type { IBooksTableFilters, IBookRequestTableFilters } from 'src/types/books';

export const BOOKS_TABLE_COLUMNS = [
  { id: 'name', label: 'Book Name', width: 310, align: 'left' }, // 250
  { id: 'category', label: 'Category', width: 150, align: 'left' },
  { id: 'availability', label: 'Availability', width: 120, align: 'center' },
  { id: 'status', label: 'Status', width: 100, align: 'center' },
  { id: 'edit', label: '', width: 50, align: 'right' },
  { id: 'delete', label: '', width: 50, align: 'right' },
];

export const BOOK_REQUESTS_TABLE_COLUMNS = [
  { id: 'name', label: 'Book Name', width: 310, align: 'left' },
  { id: 'category', label: 'Category', width: 150, align: 'left' },
  { id: 'requestedBy', label: 'Requested By', width: 180, align: 'left' },
  { id: 'requestedAt', label: 'Requested At', width: 150, align: 'left' },
  { id: 'status', label: '', width: 100, align: 'center' },
  { id: 'edit', label: '', width: 50, align: 'right' },
  { id: 'delete', label: '', width: 50, align: 'right' },
];

export const DEFAULT_BOOKS_TABLE_FILTERS: IBooksTableFilters = {
  searchKeyword: '',
  categoryId: undefined,
  isAvailable: undefined,
  isActive: undefined,
};

export const DEFAULT_BOOK_REQUESTS_TABLE_FILTERS: IBookRequestTableFilters = {
  searchKeyword: '',
  status: undefined,
  categoryId: undefined,
};

export const BOOKS_TABS = [
  { name: 'books', label: 'Books', iconifyIcon: 'solar:notes-bold' },
  { name: 'requests', label: 'Requests', iconifyIcon: 'solar:inbox-bold' },
];

// Mock data for autocomplete options
export const AUTHORS_OPTIONS = [
  'George Orwell',
  'Harper Lee',
  'Yuval Noah Harari',
  'F. Scott Fitzgerald',
  'James Clear',
  'J.K. Rowling',
  'Stephen King',
  'Agatha Christie',
  'Ernest Hemingway',
  'Jane Austen',
];

export const CATEGORIES_OPTIONS = [
  { id: '1', name: 'Fiction', color: '#E00034' },
  { id: '2', name: 'Non-Fiction', color: '#FFB000' },
  { id: '3', name: 'Self-Help', color: '#00B2FF' },
  { id: '4', name: 'Mystery', color: '#00B748' },
  { id: '5', name: 'Romance', color: '#FF6600' },
  { id: '6', name: 'Science Fiction', color: '#004270' },
];

export const SHELVES_OPTIONS = [
  { id: '1', name: 'Fiction Shelf A', location: 'Floor 1, Section A' },
  { id: '2', name: 'Non-Fiction Shelf B', location: 'Floor 1, Section B' },
  { id: '3', name: 'Self-Help Shelf C', location: 'Floor 2, Section A' },
  { id: '4', name: 'Mystery Shelf D', location: 'Floor 2, Section B' },
  { id: '5', name: 'Romance Shelf E', location: 'Floor 3, Section A' },
  { id: '6', name: 'Sci-Fi Shelf F', location: 'Floor 3, Section B' },
];
