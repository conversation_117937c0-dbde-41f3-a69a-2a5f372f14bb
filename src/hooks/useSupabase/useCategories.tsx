import type { ICategoryTableData } from 'src/types/categories';

import { useState, useEffect, useCallback } from 'react';

import { supabase } from 'src/lib/supabase';

export const useCategoryData = (table: string) => {
  const [categoryData, setCategoryData] = useState<ICategoryTableData[]>([]);
  const [loading, setIsloading] = useState(true);
  const [categoryError, setError] = useState<string>('');

  const fetchCategoryData = useCallback(async () => {
    try {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      return setCategoryData(data || []);
    } catch (error) {
      console.error('Error fetching shelves data:', error);
      return error;
    } finally {
      setIsloading(false);
    }
  }, [table]);

  const createCategory = async (data: Omit<ICategoryTableData, 'id'>) => {
    try {
      const { error } = await supabase.from(table).insert([data]);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchCategoryData();
    } catch (error) {
      console.error('Error creating shelf:', error);
      setError(error.message);
    }
  };

  const deleteCategory = async (id: string) => {
    try {
      const { error } = await supabase.from(table).delete().eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      console.log('Deleted category with ID:', id);
      fetchCategoryData();
    } catch (error) {
      console.error('Error deleting category:', error);
      setError(error.message);
    }
  };

  const updateCategory = async (id: string, updates: Partial<ICategoryTableData>) => {
    try {
      const { error } = await supabase.from(table).update(updates).eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      console.log('Updated category with ID:', id);
      fetchCategoryData();
    } catch (error) {
      console.error('Error updating category:', error);
      setError(error.message);
    }
  };

  useEffect(() => {
    fetchCategoryData();
  }, [fetchCategoryData]);

  return {
    data: categoryData,
    error: categoryError,
    loading,
    createCategory,
    updateCategory,
    deleteCategory,
  };
};
