import { useState, useEffect, useCallback } from 'react';

import { supabase } from 'src/lib/supabase';

export const useCheckById = (table: string, id: string) => {
  const [isPending, setIsPending] = useState(true);
  const [tableData, setData] = useState<any>([]);
  const shelvesIdData = useCallback(async () => {
    try {
      const { data, error } = await supabase.from(table).select('*').eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        return false;
      }
      if (data.length === 0 || !data) {
        return false;
      }
      return setData(data || []);
    } catch (error) {
      console.error('Error checking by id:', error);
      return false;
    } finally {
      setIsPending(false);
    }
  }, [table, id]);

  useEffect(() => {
    shelvesIdData();
  }, [shelvesIdData]);

  return { data: tableData, isPending };
};
