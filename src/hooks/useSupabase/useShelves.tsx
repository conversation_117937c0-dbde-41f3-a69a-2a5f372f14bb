import type { IShelvesItem } from 'src/types/shelves';

import { useState, useEffect, useCallback } from 'react';

import { supabase } from 'src/lib/supabase';

export const useShelvesData = (table: string) => {
  const [shelvesData, setData] = useState<IShelvesItem[]>([]);
  const [loading, setIsloading] = useState(true);
  const [shelvesError, setError] = useState<string>('');

  const fetchShelvesData = useCallback(async () => {
    try {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      return setData(data || []);
    } catch (error) {
      console.error('Error fetching shelves data:', error);
      return error;
    } finally {
      setIsloading(false);
    }
  }, [table]);

  const createShelf = async (shelfData: Omit<IShelvesItem, 'id'>) => {
    try {
      const { error } = await supabase.from(table).insert([shelfData]);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchShelvesData();
    } catch (error) {
      console.error('Error creating shelf:', error);
      setError(error.message);
    }
  };

  const deleteShelf = async (id: string) => {
    try {
      const { error } = await supabase.from(table).delete().eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchShelvesData();
    } catch (error) {
      console.error('Error deleting shelf:', error);
      setError(error.message);
    }
  };

  const updateShelf = async (id: string, updates: Partial<IShelvesItem>) => {
    try {
      const { error } = await supabase.from(table).update(updates).eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchShelvesData();
    } catch (error) {
      console.error('Error updating shelf:', error);
      setError(error.message);
    }
  };

  useEffect(() => {
    fetchShelvesData();
  }, [fetchShelvesData]);

  return { data: shelvesData, error: shelvesError, loading, createShelf, updateShelf, deleteShelf };
};
