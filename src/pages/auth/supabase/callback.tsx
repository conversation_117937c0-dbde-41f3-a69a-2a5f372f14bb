import { useEffect } from 'react';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { supabase } from 'src/lib/supabase';

import { useAuthContext } from 'src/auth/hooks';

export default function AuthCallback() {
  const router = useRouter();
  const { checkUserSession } = useAuthContext();

  useEffect(() => {
    const handleAuthCallbackFlow = async () => {
      try {
        // const session = await handleOAuthCallback();
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();
        console.log(session, 'this is session');
        if (error) {
          console.error('Error getting session:', error);
          throw error;
        }

        if (session?.user) {
          await checkUserSession?.();
          router.push(paths.dashboard.shelves.root);
        } else {
          router.push(paths.auth.supabase.signIn);
        }
      } catch (err) {
        console.error('Unhandled callback error:', err);
        router.push(paths.auth.supabase.signIn);
      }
    };

    handleAuthCallbackFlow();
  }, [router, checkUserSession]);

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <p>Processing authentication...</p>
    </div>
  );
}
