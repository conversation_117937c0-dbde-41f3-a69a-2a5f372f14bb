import type { IShelvesItem } from 'src/types/shelves';

import { useBoolean } from 'src/hooks/use-boolean';
import { useShelvesData } from 'src/hooks/useSupabase/useShelves';

import { EmptyContent } from 'src/components/empty-content';
import { DashboardPageWrapper } from 'src/components/dashboard-page-wrapper';

import { ShelvesList } from 'src/sections/admin/shelves/section/shelves';
import ShelfNewDialog from 'src/sections/admin/shelves/shelf-new-dialog';
import { ShelvesSkeletonList } from 'src/sections/admin/shelves/section/shelves-skeleton';

export default function ShelvesManagementPage() {
  const dialog = useBoolean();

  const { data, loading, createShelf, deleteShelf, updateShelf } = useShelvesData('shelves');

  const shelvesData: IShelvesItem[] = Array.isArray(data) ? data : [];

  const handleCreateShelf = async (shelfData: Omit<IShelvesItem, 'id'>) => {
    try {
      await createShelf(shelfData);
      dialog.onFalse(); // Close dialog after successful creation
    } catch (error) {
      console.error('Error creating shelf:', error);
    }
  };

  const handleDeleteShelf = async (id: string) => {
    try {
      await deleteShelf(id);
    } catch (error) {
      console.error('Error deleting shelf:', error);
    }
  };

  const handleUpdateShelf = async (id: string, updates: Partial<IShelvesItem>) => {
    try {
      await updateShelf(id, updates);
    } catch (error) {
      console.error('Error updating shelf:', error);
    }
  };

  return (
    <DashboardPageWrapper
      pageTitle="Shelf: List"
      heading="Shelves"
      breadcrumbLinks={[{ name: 'Dashboard' }, { name: 'Shelves' }]}
      actionButton={{
        label: 'New Shelf',
        onClick: dialog.onTrue,
        show: true,
      }}
    >
      {loading ? (
        <ShelvesSkeletonList />
      ) : shelvesData.length === 0 ? (
        <EmptyContent
          title="No shelves are there!"
          imgUrl="/assets/icons/empty/ic_secondary_shape.svg"
          actionButton={{
            label: 'Add One',
            onClick: dialog.onTrue,
          }}
        />
      ) : (
        <ShelvesList
          categories={shelvesData}
          onUpdateShelf={handleUpdateShelf}
          onDeleteShelf={handleDeleteShelf}
        />
      )}

      {dialog.value && <ShelfNewDialog dialog={dialog} onCreateShelf={handleCreateShelf} />}
    </DashboardPageWrapper>
  );
}
