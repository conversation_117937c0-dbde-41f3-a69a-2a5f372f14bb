import { useState } from 'react';

import { Tab, Tabs } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useParams, useSearchParams } from 'src/routes/hooks';

import { useCheckById } from 'src/hooks/useSupabase/useCheckById';

import { SHELF_TABS } from 'src/constants/shelves';

import { Iconify } from 'src/components/iconify';
import { DashboardPageWrapper } from 'src/components/dashboard-page-wrapper';

import ShelfEditViewBooks from 'src/sections/admin/shelves/section/shelf-edit-view-books';
import ShelfEditViewGeneral from 'src/sections/admin/shelves/section/shelves-edit-view-general';

export default function CategoryEditView() {
  const { shelfId } = useParams();

  const searchParam = useSearchParams();
  const isNew = searchParam.get('new') === 'true';

  const { data, isPending } = useCheckById('shelves', shelfId!);

  const [currentTab, setCurrentTab] = useState<string>(SHELF_TABS[isNew ? 1 : 0].name);

  return (
    <DashboardPageWrapper
      pageTitle="Shelf: List"
      heading="Shelves"
      breadcrumbLinks={[
        { name: 'Dashboard', href: '#' },
        { href: paths.dashboard.shelves.root, name: 'Shelves' },
        { name: data[0]?.name },
      ]}
    >
      <Tabs value={currentTab} onChange={(e, newVal) => setCurrentTab(newVal)}>
        {SHELF_TABS.map((type) => (
          <Tab
            key={type.name}
            sx={{ fontWeight: 'fontWeightBold' }}
            value={type.name}
            iconPosition="start"
            icon={<Iconify icon={type.iconifyIcon} width={24} />}
            label={type.name}
          />
        ))}
      </Tabs>

      {currentTab === 'general' && data && <ShelfEditViewGeneral shelf={data[0]} />}

      {currentTab === 'books' && !isPending && <ShelfEditViewBooks />}
    </DashboardPageWrapper>
  );
}
