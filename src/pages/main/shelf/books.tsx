import type { IBookCard } from 'src/types/books';

import { useState, useEffect } from 'react';

import { Stack } from '@mui/material';

import BooksList from 'src/sections/main/shelf/books/section/books-list';
import FilterOptions from 'src/sections/main/shelf/books/section/filter-options';
import BooksListEmpty from 'src/sections/main/shelf/books/section/books-list-empty';
import BooksListLoading from 'src/sections/main/shelf/books/section/books-list-loading';

import { booksData } from 'src/types/books';

// ----------------------------------------------------------------------

export default function ShelfBooksView() {
  const [loading, setIsloading] = useState(true);

  useEffect(() => {
    setTimeout(() => setIsloading(false), 1000);
  }, []);

  const { data }: { data: IBookCard[]; isPending: boolean } = {
    data: booksData,
    isPending: true,
  };

  return (
    <Stack gap={2}>
      <FilterOptions />
      {loading ? (
        <BooksListLoading />
      ) : data.length === 0 ? (
        <BooksListEmpty />
      ) : (
        <BooksList books={data} />
      )}
    </Stack>
  );
}
