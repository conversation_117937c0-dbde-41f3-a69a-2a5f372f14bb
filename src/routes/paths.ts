const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/admin',
};

export const paths = {
  faqs: '/faqs',

  // AUTH
  auth: {
    firebase: {
      signIn: `${ROOTS.AUTH}/firebase/sign-in`,
      verify: `${ROOTS.AUTH}/firebase/verify`,
      signUp: `${ROOTS.AUTH}/firebase/sign-up`,
      resetPassword: `${ROOTS.AUTH}/firebase/reset-password`,
    },
    supabase: {
      signIn: `${ROOTS.AUTH}/supabase/sign-in`,
      verify: `${ROOTS.AUTH}/supabase/verify`,
      signUp: `${ROOTS.AUTH}/supabase/sign-up`,
      updatePassword: `${ROOTS.AUTH}/supabase/update-password`,
      resetPassword: `${ROOTS.AUTH}/supabase/reset-password`,
    },
  },

  // DASHBOARD
  dashboard: {
    shelves: {
      root: `${ROOTS.DASHBOARD}/shelves`,
      detail: (id: string) => `${ROOTS.DASHBOARD}/shelves/${id}`,
    },
    books: `${ROOTS.DASHBOARD}/books`,
    categories: `${ROOTS.DASHBOARD}/categories`,
    users: `${ROOTS.DASHBOARD}/users`,
    accountSettings: `${ROOTS.DASHBOARD}/account-settings`,
  },

  // USER
  public: {
    shelves: {
      books: (shelfId: string) => `/shelf/${shelfId}`,
    },
    books: {
      detail: (bookId: string) => `/shelf/book/${bookId}`,
    },
  },
};
