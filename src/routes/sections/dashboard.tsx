import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { DashboardLayout } from 'src/layouts/dashboard';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const ShelvesManagementPage = lazy(() => import('src/pages/dashboard/shelves/shelves-management'));
const SingleShelfPage = lazy(() => import('src/pages/dashboard/shelves/single-shelf'));
const AdminAccountSettingsPage = lazy(() => import('src/pages/dashboard/three'));
const BooksManagementPage = lazy(() => import('src/pages/dashboard/books/books-management'));
const CategoriesManagementPage = lazy(
  () => import('src/pages/dashboard/categories/categories-management')
);
const UserManagementPage = lazy(() => import('src/pages/dashboard/six'));

// ----------------------------------------------------------------------

const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

export const dashboardRoutes = [
  {
    path: 'admin',
    element: CONFIG.auth.skip ? <>{layoutContent}</> : <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      {
        path: 'shelves',
        children: [
          { element: <ShelvesManagementPage />, index: true },
          {
            path: ':shelfId',
            element: <SingleShelfPage />,
          },
        ],
      },
      { path: 'books', element: <BooksManagementPage /> },
      { path: 'categories', element: <CategoriesManagementPage /> },
      { path: 'users', element: <UserManagementPage /> },
      { path: 'account-settings', element: <AdminAccountSettingsPage /> },
    ],
  },
];
