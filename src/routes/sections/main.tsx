import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { PublicLayout } from 'src/layouts/public';

import { LoadingScreen } from 'src/components/loading-screen';

import SingleBook from 'src/sections/main/shelf/books/single-book/single-book';

import { AuthGuard } from 'src/auth/guard';

// Main Pages
const ShelfBooksPage = lazy(() => import('src/pages/main/shelf/books'));

// Error
const Page404 = lazy(() => import('src/pages/error/404'));

// ----------------------------------------------------------------------

const layoutContent = (
  <PublicLayout content={{ compact: true }}>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </PublicLayout>
);

export const mainRoutes = [
  {
    path: '/',
    element: CONFIG.auth.skip ? <>{layoutContent}</> : <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      {
        path: 'shelf',
        children: [
          {
            path: ':shelfId',
            element: <ShelfBooksPage />,
          },
          {
            path: 'book/:bookId',
            element: <SingleBook />,
          },
        ],
      },
      { path: '404', element: <Page404 /> },
    ],
  },
];
