import type { BoxProps } from '@mui/material/Box';
import type { StackProps } from '@mui/material/Stack';
import type { IBookRequestTableFilters } from 'src/types/books';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';

import { CATEGORIES_OPTIONS } from 'src/constants/books';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

type Props = StackProps & {
  filters: IBookRequestTableFilters;
  onFilters: (
    name: keyof IBookRequestTableFilters,
    value: IBookRequestTableFilters[keyof IBookRequestTableFilters]
  ) => void;
  onResetFilters: VoidFunction;
  results: number;
};

export default function BookRequestsTableFiltersResult({
  filters,
  onFilters,
  onResetFilters,
  results,
  ...other
}: Props) {
  const handleRemoveKeyword = () => {
    onFilters('searchKeyword', '');
  };

  const handleRemoveCategory = () => {
    onFilters('categoryId', undefined);
  };

  const handleRemoveStatus = () => {
    onFilters('status', undefined);
  };

  const getCategoryName = (categoryId: string) => {
    const category = CATEGORIES_OPTIONS.find((cat) => cat.id === categoryId);
    return category?.name || categoryId;
  };

  const getStatusLabel = (status: string) => status.charAt(0).toUpperCase() + status.slice(1);

  return (
    <Stack spacing={1.5} {...other}>
      <Box sx={{ typography: 'body2' }}>
        <strong>{results}</strong>
        <Box component="span" sx={{ color: 'text.secondary', ml: 0.25 }}>
          results found
        </Box>
      </Box>

      <Stack flexGrow={1} spacing={1} direction="row" flexWrap="wrap" alignItems="center">
        {filters.searchKeyword && (
          <Block label="Keyword:">
            <Chip size="small" label={filters.searchKeyword} onDelete={handleRemoveKeyword} />
          </Block>
        )}

        {filters.categoryId && (
          <Block label="Category:">
            <Chip
              size="small"
              label={getCategoryName(filters.categoryId)}
              onDelete={handleRemoveCategory}
            />
          </Block>
        )}

        {filters.status && (
          <Block label="Status:">
            <Chip
              size="small"
              label={getStatusLabel(filters.status)}
              onDelete={handleRemoveStatus}
            />
          </Block>
        )}

        <Button
          color="error"
          onClick={onResetFilters}
          startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
        >
          Clear
        </Button>
      </Stack>
    </Stack>
  );
}

// ----------------------------------------------------------------------

type BlockProps = BoxProps & {
  label: string;
};

function Block({ label, children, sx, ...other }: BlockProps) {
  return (
    <Stack
      component={Paper}
      variant="outlined"
      spacing={1}
      direction="row"
      sx={{
        p: 1,
        borderRadius: 1,
        overflow: 'hidden',
        borderStyle: 'dashed',
        ...sx,
      }}
      {...other}
    >
      <Box component="span" sx={{ typography: 'subtitle2' }}>
        {label}
      </Box>

      <Stack spacing={1} direction="row" flexWrap="wrap">
        {children}
      </Stack>
    </Stack>
  );
}
