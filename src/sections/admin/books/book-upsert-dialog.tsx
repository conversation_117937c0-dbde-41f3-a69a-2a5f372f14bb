import type { IBooksTableData } from 'src/types/books';
import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import { z as zod } from 'zod';
import { useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { Grid, TextField, Autocomplete } from '@mui/material';

import { AUTHORS_OPTIONS, SHELVES_OPTIONS, CATEGORIES_OPTIONS } from 'src/constants/books';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Form, RHFTextField, RHFAutocomplete } from 'src/components/hook-form';

const defaultValues = {
  name: '',
  author: '',
  categoryId: '',
  shelfId: '',
  description: '',
};

function BookUpsertDialog({
  dialogControl,
  book,
  onClose,
  onCreateBook,
  onUpdateBook,
}: {
  dialogControl: IUseBooleanReturn;
  book: IBooksTableData | null;
  onClose: () => void;
  onCreateBook: (bookData: Omit<IBooksTableData, 'id'>) => Promise<void>;
  onUpdateBook: (id: string, updates: Partial<IBooksTableData>) => Promise<void>;
}) {
  const NewBookSchema = zod.object({
    name: zod.string().min(1, 'Book name is required'),
    author: zod.string().min(1, 'Author is required'),
    categoryId: zod.string().min(1, 'Category is required'),
    shelfId: zod.string().min(1, 'Shelf is required'),
    description: zod.string().min(1, 'Description is required'),
  });

  const methods = useForm({
    resolver: zodResolver(NewBookSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    control,
    watch,
    formState: { isSubmitting },
    reset,
  } = methods;

  console.log(watch());

  const isUpdating = !!book;

  useEffect(() => {
    if (book) {
      // Transform book data to match form field names
      const formData = {
        name: book.name,
        author: book.author,
        categoryId: book.category?.id || '',
        shelfId: book.shelf?.id || '',
        description: book.description,
      };
      reset(formData);
    } else {
      reset(defaultValues);
    }
  }, [book, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      // Find the selected category and shelf objects
      const selectedCategory = CATEGORIES_OPTIONS.find((cat) => cat.id === data.categoryId);
      const selectedShelf = SHELVES_OPTIONS.find((shelf) => shelf.id === data.shelfId);

      // Transform form data to match database schema
      const bookData = {
        name: data.name,
        author: data.author,
        category: selectedCategory
          ? {
              id: selectedCategory.id,
              name: selectedCategory.name,
              color: selectedCategory.color,
              isPublished: true,
            }
          : null,
        shelf: selectedShelf
          ? {
              id: selectedShelf.id,
              name: selectedShelf.name,
              location: selectedShelf.location,
            }
          : null,
        description: data.description,
        isAvailable: true,
        isActive: true,
      };

      if (isUpdating && book?.id) {
        // Update existing book
        await onUpdateBook(book.id, bookData);
        toast.success('Book updated successfully!');
        console.log('Book updated successfully');
      } else {
        // Create new book
        await onCreateBook(bookData);
        toast.success('Book created successfully!');
        console.log('Book created successfully');
      }

      closeDialog();
    } catch (error) {
      console.error('Error saving book:', error);
      toast.error('Failed to save book. Please try again.');
    }
  });

  const closeDialog = () => {
    reset(defaultValues);
    onClose();
  };

  return (
    <Dialog
      open={dialogControl.value}
      onClose={closeDialog}
      fullWidth
      maxWidth="md"
      sx={{
        '& .MuiPaper-elevation': {
          maxWidth: '800px',
        },
      }}
    >
      <Form methods={methods} onSubmit={onSubmit}>
        <DialogTitle>
          <Typography variant="h6" component="span" fontWeight="bold">
            {isUpdating ? `Edit: ${book?.name}` : 'New Book'}
          </Typography>
        </DialogTitle>

        <IconButton
          aria-label="close"
          onClick={closeDialog}
          sx={{
            position: 'absolute',
            right: 8,
            top: 20,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            pt: 2,
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <RHFTextField
                name="name"
                label="Book Name"
                size="small"
                fullWidth
                placeholder="Enter book title"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              {/* <Autocomplete freeSolo renderInput={(params) => <TextField {...params} />} /> */}
              <RHFAutocomplete
                name="author"
                label="Author"
                size="small"
                freeSolo
                options={AUTHORS_OPTIONS}
                renderOption={(props, option) => (
                  <li {...props} key={option}>
                    {option}
                  </li>
                )}
                onInputChange={(e, f) => {
                  console.log('e', e);
                  console.log('f', f);
                }}
                // onInputChange={(_, val) => setSearchText(val)}
              />
              {/* <Controller
                name="author"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    {...field}
                    options={AUTHORS_OPTIONS}
                    freeSolo
                    value={field.value}
                    onChange={(event, newValue) => {
                      console.log('🚀 ~ newValue:', newValue);
                      field.onChange(newValue || '');
                    }}
                    onInputChange={(e, h) => console.log('🚀 ~ e.target.value:', e.target, 'h', h)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Author"
                        size="small"
                        fullWidth
                        placeholder="Select or enter author name"
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              /> */}
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="categoryId"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    {...field}
                    options={CATEGORIES_OPTIONS}
                    getOptionLabel={(option) => option.name}
                    value={CATEGORIES_OPTIONS.find((cat) => cat.id === field.value) || null}
                    onChange={(event, newValue) => {
                      field.onChange(newValue?.id || '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Category"
                        size="small"
                        fullWidth
                        placeholder="Select category"
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="shelfId"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    {...field}
                    options={SHELVES_OPTIONS}
                    getOptionLabel={(option) => `${option.name} (${option.location})`}
                    value={SHELVES_OPTIONS.find((shelf) => shelf.id === field.value) || null}
                    onChange={(event, newValue) => {
                      field.onChange(newValue?.id || '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Shelf"
                        size="small"
                        fullWidth
                        placeholder="Select shelf"
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <RHFTextField
                name="description"
                label="Description"
                size="small"
                rows={4}
                fullWidth
                multiline
                placeholder="Enter book description"
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={closeDialog} variant="outlined" color="inherit">
            Cancel
          </Button>
          <LoadingButton color="primary" type="submit" variant="contained" loading={isSubmitting}>
            {isUpdating ? 'Update Book' : 'Create Book'}
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}

export default BookUpsertDialog;
