import type { StackProps } from '@mui/material/Stack';
import type { ICategoryTableFilters } from 'src/types/categories';

import { useCallback } from 'react';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

type Props = StackProps & {
  filters: ICategoryTableFilters;
  onFilters: <K extends keyof ICategoryTableFilters>(
    name: K,
    value: ICategoryTableFilters[K]
  ) => void;
  //
  onResetFilters: VoidFunction;
  //
  results: number;
};

export default function CategoriesTableFiltersResult({
  filters,
  onFilters,
  //
  onResetFilters,
  //
  results,
  ...other
}: Props) {
  const handleRemoveKeyword = useCallback(() => {
    onFilters('searchKeyword', '');
  }, [onFilters]);

  const handleRemoveRole = useCallback(() => {
    onFilters('isActive', undefined);
  }, [onFilters]);

  return (
    <Stack spacing={1.5} {...other}>
      <Box sx={{ typography: 'body2' }}>
        <strong>{results}</strong>
        <Box component="span" sx={{ color: 'text.secondary', ml: 0.25 }}>
          {results === 1 ? 'result found' : 'results found'}
        </Box>
      </Box>

      <Stack flexGrow={1} spacing={1} direction="row" flexWrap="wrap">
        {filters.isActive !== undefined && (
          <Block label="Status:">
            <Chip
              label={filters.isActive ? 'active' : 'inactive'}
              size="small"
              onDelete={() => handleRemoveRole()}
            />
          </Block>
        )}

        {!!filters.searchKeyword && (
          <Block label="Keyword">
            <Chip label={filters.searchKeyword} size="small" onDelete={handleRemoveKeyword} />
          </Block>
        )}

        <Button
          color="error"
          onClick={onResetFilters}
          startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
        >
          Clear
        </Button>
      </Stack>
    </Stack>
  );
}

// ----------------------------------------------------------------------

type BlockProps = StackProps & {
  label: string;
};

function Block({ label, children, sx, ...other }: BlockProps) {
  return (
    <Stack
      component={Paper}
      variant="outlined"
      spacing={1}
      direction="row"
      sx={{
        p: 1,
        borderRadius: 1,
        overflow: 'hidden',
        borderStyle: 'dashed',
        ...sx,
      }}
      {...other}
    >
      <Box component="span" sx={{ typography: 'subtitle2' }}>
        {label}
      </Box>

      <Stack spacing={1} direction="row" flexWrap="wrap">
        {children}
      </Stack>
    </Stack>
  );
}
