import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import * as zod from 'zod';
import { useForm, Controller } from 'react-hook-form';
// import { useQueryClient } from '@tanstack/react-query';

import type { ICategoryTableData } from 'src/types/categories';

import { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';

import { LoadingButton } from '@mui/lab';
import {
  Box,
  Grid,
  Button,
  Dialog,
  Typography,
  IconButton,
  FormControl,
  DialogTitle,
  DialogActions,
  DialogContent,
} from '@mui/material';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { ColorPicker } from 'src/components/color-utils';
// import FormProvider from 'src/components/hook-form/form-provider';
import { Form, RHFSwitch, RHFTextField } from 'src/components/hook-form';

// import { CATEGORY_COLORS } from 'src/constants/categories';
export const CATEGORY_COLORS = ['#E00034', '#FFB000', '#00B2FF', '#00B748', '#FF6600', '#004270'];

const defaultValues = {
  name: '',
  description: '',
  color: CATEGORY_COLORS[0],
  isActive: false,
};

function CategoryUpsertDialog({
  dialogControl,
  category,
  onClose,
  createCategory,
  updateCategory,
}: {
  dialogControl: IUseBooleanReturn;
  category: ICategoryTableData | null;
  onClose: () => void;
  createCategory: (categoryData: Omit<ICategoryTableData, 'id'>) => Promise<void>;
  updateCategory: (id: string, updates: Partial<ICategoryTableData>) => Promise<void>;
}) {
  // const router = useRouter();

  const NewCategorySchema = zod.object({
    name: zod.string(),
    description: zod.string(),
    color: zod.string().default(CATEGORY_COLORS[0]),
    isActive: zod.boolean(),
  });

  const methods = useForm({
    resolver: zodResolver(NewCategorySchema),
    defaultValues,
  });

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
    reset,
  } = methods;

  const isUpdating = !!category;
  console.log('🚀 ~ isUpdating:', isUpdating);

  useEffect(() => {
    console.log('use effect triggered');
    console.log('🚀 ~ useEffect ~ category:', category);
    if (category) {
      // Transform category data to match form field names
      const formData = {
        name: category.name,
        description: category.description,
        color: category.color,
        isActive: category.is_status || false, // Transform is_status to isActive for form
      };
      reset(formData);
    } else {
      reset(defaultValues);
    }
  }, [category, reset]);

  const onSubmit = handleSubmit(async (data) => {
    console.log('🚀 ~ onSubmit ~ data:', data);

    try {
      // Transform form data to match database schema
      const categoryData = {
        name: data.name,
        description: data.description,
        color: data.color,
        is_status: data.isActive, // Transform isActive to is_status for database
      };

      if (isUpdating && category?.id) {
        // Update existing category
        await updateCategory(category.id, categoryData);
        toast.success('Category updated successfully!');
        console.log('Category updated successfully');
      } else {
        // Create new category
        await createCategory(categoryData);
        toast.success('Category created successfully!');
        console.log('Category created successfully');
      }

      closeDialog();
    } catch (error) {
      console.error('Error saving category:', error);
      toast.error('Failed to save category. Please try again.');
    }
  });

  const closeDialog = () => {
    reset(defaultValues);
    onClose();
  };

  return (
    <Dialog
      open={dialogControl.value}
      onClose={closeDialog}
      fullWidth
      sx={{
        '& .MuiPaper-elevation': {
          maxWidth: '720px',
        },
      }}
    >
      <Form methods={methods} onSubmit={onSubmit}>
        <DialogTitle>
          <Typography variant="h6" component="span" fontWeight="bold">
            {isUpdating ? category.name : 'New Category'}
          </Typography>
        </DialogTitle>

        <IconButton
          aria-label="close"
          onClick={closeDialog}
          sx={{
            position: 'absolute',
            right: 8,
            top: 20,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
          }}
        >
          <RHFTextField name="name" label="Name" size="small" margin="dense" fullWidth />
          <RHFTextField
            name="description"
            label="Description"
            size="small"
            margin="dense"
            rows={4}
            fullWidth
            multiline
          />
          <Box>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={5}>
                <FormControl>
                  <Typography
                    component={Typography}
                    variant="subtitle2"
                    fontWeight="fontWeightBold"
                    marginBottom={1}
                  >
                    Colors
                  </Typography>
                  <Controller
                    name="color"
                    control={control}
                    render={({ field }) => (
                      <ColorPicker
                        colors={CATEGORY_COLORS}
                        selected={field.value}
                        onSelectColor={(color) => field.onChange(color as string)}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          <Box sx={{ flexGrow: 1 }} flexDirection="column" display="flex">
            <Typography
              component={Typography}
              variant="subtitle2"
              fontWeight="fontWeightBold"
              marginBottom={1}
            >
              Status
            </Typography>
            <RHFSwitch name="isActive" label="Show/Hide" />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={closeDialog} variant="outlined" color="inherit">
            Cancel
          </Button>
          <LoadingButton color="success" type="submit" variant="contained" loading={isSubmitting}>
            {isUpdating ? 'Update' : 'Create'}
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}

export default CategoryUpsertDialog;
