import type { ICategoryTableFilters } from 'src/types/categories';

import v from 'voca';

import {
  Stack,
  Select,
  MenuItem,
  TextField,
  InputLabel,
  FormControl,
  OutlinedInput,
  InputAdornment,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

type props = {
  filters: ICategoryTableFilters;
  onFilters: <K extends keyof ICategoryTableFilters>(
    name: K,
    value: ICategoryTableFilters[K]
  ) => void;
};

const STATUS = [
  {
    label: 'all',
    value: undefined,
  },
  {
    label: 'active',
    value: true,
  },
  {
    label: 'inactive',
    value: false,
  },
];

export default function CategoriesTableToolbar({ filters, onFilters }: props) {
  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{
        xs: 'column',
        md: 'row',
      }}
      sx={{
        p: 2.5,
      }}
    >
      <FormControl
        sx={{
          flexShrink: 0,
          width: { xs: 1, md: 287 },
        }}
      >
        <InputLabel id="type-multiple-checkbox-label">Status</InputLabel>

        <Select
          labelId="type-multiple-checkbox-label"
          id="type-multiple-checkbox"
          value={filters.isActive === undefined ? 'all' : filters.isActive}
          onChange={(e) => {
            const { value } = e.target;
            onFilters('isActive', value === 'all' ? undefined : (value as boolean));
          }}
          input={<OutlinedInput label="Status" />}
          sx={{ textTransform: 'capitalize' }}
        >
          {STATUS.map((option) => (
            <MenuItem
              key={option.label}
              value={option.value === undefined ? 'all' : String(option.value)}
            >
              {v.titleCase(option.label)}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <Stack direction="row" alignItems="center" spacing={2} flexGrow={1} sx={{ width: 1 }}>
        <TextField
          fullWidth
          value={filters.searchKeyword}
          onChange={(e) => onFilters('searchKeyword', e.target.value)}
          placeholder="Search categories by name or description..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
            endAdornment: filters.searchKeyword && (
              <InputAdornment position="end">
                <Iconify
                  icon="eva:close-fill"
                  sx={{
                    color: 'text.disabled',
                    cursor: 'pointer',
                    '&:hover': { color: 'text.primary' },
                  }}
                  onClick={() => onFilters('searchKeyword', '')}
                />
              </InputAdornment>
            ),
          }}
        />
      </Stack>
    </Stack>
  );
}
