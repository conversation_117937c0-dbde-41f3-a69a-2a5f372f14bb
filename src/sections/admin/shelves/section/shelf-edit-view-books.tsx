import { isEqual } from 'lodash';
import { useMemo, useState, useCallback } from 'react';

import { Card, Table, TableBody, TableContainer } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

// import { useGetTests } from 'src/api/staff/tests';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content/empty-content';
import {
  useTable,
  emptyRows,
  TableNoData,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import {
  booksTableData,
  type ITestTableItem,
  type ITestsTableFilters,
  type ITestsTableFilterValue,
} from 'src/types/books';

import TestsTableRow from '../test-table-row';
import TestNewDialog from '../test-new-dialog';
import TestsTableToolbar from '../test-table-toolbar';
import TestTableFiltersResult from '../test-table-filters-result';

const defaultFilters: ITestsTableFilters = {
  categoryId: '',
  categoryName: '',
  searchQuery: '',
  status: '',
};

function ShelfEditViewBooks({ questionId }: { questionId?: string }) {
  const newTestDialog = useBoolean();

  const TABLE_HEAD = useMemo(
    () => [
      { id: 'name', label: 'Name', width: 300, align: 'left' },
      { id: 'category', label: 'Category', width: 220, align: 'left' },
      { id: 'availability', label: 'Availability', width: 132, align: 'left' },
      { id: 'enable', label: 'Published', width: 50, align: 'right' },
      { id: 'open', label: 'Open', width: 50, align: 'right' },
      { id: 'share', label: 'Share', width: 50, align: 'right' },
      { id: 'menu', label: '', width: 50, align: 'right' },
      { id: 'delete', label: '', width: 50, align: 'right' },
      { id: 'menu', label: '', width: 50, align: 'right' },
    ],
    []
  );

  const isQuestionTestTabView = Boolean(questionId);

  const table = useTable();

  const [filters, setFilters] = useState(defaultFilters);
  // const [duplicateTestData, setDuplicateTestData] = useState(null);
  const [testDuplicationId, setTestDuplicationId] = useState<string | null>(null);
  console.log('🚀 ~ ShelfEditViewBooks ~ testDuplicationId:', testDuplicationId);

  const canReset = !isEqual(defaultFilters, filters);

  const denseHeight = table.dense ? 56 : 56 + 20;

  // const tableFiltersHandler = useCallback(
  //   ({ fetchAll = false }: { fetchAll?: boolean }) => ({
  //     ...(fetchAll
  //       ? { all: true }
  //       : {
  //           page: table.page + 1,
  //           pageSize: table.rowsPerPage,
  //         }),
  //     sortColumn: table.orderBy,
  //     sortOrder: table.order,
  //   }),
  //   [table]
  // );

  // const apiFiltersHandler = useCallback(() => {
  //   const allFilters: ITestsTableFilters = { ...filters };
  //   if (allFilters.searchQuery) {
  //     delete allFilters.searchQuery;
  //   }

  //   if (allFilters.status) {
  //     allFilters.isPublished = allFilters.status === 'Published';
  //   }

  //   return allFilters;
  // }, [filters]);

  // const [debouncedsearchQuery] = useDebounce(filters.searchQuery || '', 1500);

  // const apiFilters: ITestsTableFilters = useMemo(() => {
  //   const allFilters = apiFiltersHandler();
  //   const tableFilters = tableFiltersHandler({ fetchAll: false });

  //   return removeFalsyValuesFromObject({
  //     ...allFilters,
  //     ...tableFilters,
  //     searchQuery: debouncedsearchQuery,
  //   });
  // }, [apiFiltersHandler, tableFiltersHandler, debouncedsearchQuery]);

  // const { data, isFetching }: any = useGetTests({
  //   ...apiFilters,
  //   questionId,
  // });

  const { data, isFetching }: any = {
    data: { data: booksTableData },
    isFetching: false,
  };

  const {
    tests: tableData = [],
    totalCount = 0,
  }: {
    tests: ITestTableItem[];
    totalCount: number;
  } = useMemo(
    () => ({
      tests: data?.data,
      totalCount: data?.totalCount,
    }),
    [data]
  );
  console.log('🚀 ~ ShelfEditViewBooks ~ tests:', tableData);

  const notFound = (!tableData.length && canReset) || !tableData.length;

  const handleFilters = useCallback(
    (name: string, value: ITestsTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  const handleTestDuplication = useCallback(
    (testId: string) => {
      // const formattedTestData = {
      //   ...testData,
      //   levels: testData.levels
      //     ? testData.levels.map(({ id, ...rest }: { id: string; rest: any }) => ({
      //         ...rest,
      //       }))
      //     : [],
      // };
      // const originalTestData =
      // setDuplicateTestData(formattedTestData);
      setTestDuplicationId(testId);
      newTestDialog.onTrue();
    },
    [newTestDialog]
  );

  return (
    <>
      <Card>
        {isFetching ||
        // debouncedsearchQuery.length > 0 ||
        canReset ||
        isQuestionTestTabView ||
        tableData.length ? (
          <>
            <TestsTableToolbar filters={filters} onFilters={handleFilters} />

            {canReset && (
              <TestTableFiltersResult
                filters={filters}
                onFilters={handleFilters}
                onResetFilters={handleResetFilters}
                results={totalCount}
                sx={{ p: 2.5, pt: 0 }}
              />
            )}

            <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
              <Scrollbar>
                <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                  <TableHeadCustom
                    order={table.order}
                    orderBy={table.orderBy}
                    headLabel={TABLE_HEAD}
                    rowCount={20}
                    numSelected={table.selected.length}
                    onSort={table.onSort}
                    sx={{ whiteSpace: 'nowrap' }}
                  />

                  <TableBody>
                    {tableData.length > 0 &&
                      tableData.map((row) => (
                        <TestsTableRow
                          key={row.id}
                          row={row}
                          handleTestDuplication={handleTestDuplication}
                        />
                      ))}

                    <TableEmptyRows
                      height={denseHeight}
                      emptyRows={emptyRows(table.page, table.rowsPerPage, tableData.length)}
                    />

                    <TableNoData subTitle="" title="" show={notFound} />
                  </TableBody>
                </Table>
              </Scrollbar>
            </TableContainer>

            <TablePaginationCustom
              count={totalCount}
              page={table.page}
              rowsPerPage={table.rowsPerPage}
              onPageChange={table.onChangePage}
              onRowsPerPageChange={table.onChangeRowsPerPage}
              dense={table.dense}
              onChangeDense={table.onChangeDense}
            />
          </>
        ) : (
          <EmptyContent
            title="No books are there!"
            imgUrl="/assets/icons/empty/ic_secondary_shape.svg"
            actionButton={{
              label: 'Add One',
              onClick: newTestDialog.onTrue,
            }}
          />
        )}
      </Card>
      {newTestDialog.value && (
        <TestNewDialog
        // dialog={newTestDialog}
        // testDuplicationId={testDuplicationId}
        // setTestDuplicationId={setTestDuplicationId}
        />
      )}
    </>
  );
}

export default ShelfEditViewBooks;
