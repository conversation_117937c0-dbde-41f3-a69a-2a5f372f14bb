import { z } from 'zod';
import { QRCodeSVG } from 'qrcode.react';
import { useForm } from 'react-hook-form';
import { useMemo, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';

import { LoadingButton } from '@mui/lab';
import {
  Box,
  Paper,
  Stack,
  Button,
  Dialog,
  Typography,
  IconButton,
  FormControl,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';
import { useShelvesData } from 'src/hooks/useSupabase/useShelves';

import { CONFIG } from 'src/config-global';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { RHFTextField } from 'src/components/hook-form';
import { Form } from 'src/components/hook-form/form-provider';
import { RHFSwitch } from 'src/components/hook-form/rhf-switch';

// Zod Schema
const ShelfEditSchema = z.object({
  name: z
    .string()
    .min(1, 'Shelf name is required')
    .max(100, 'Shelf name must be less than 100 characters')
    .trim(),
  location: z
    .string()
    .min(1, 'Shelf location is required')
    .max(200, 'Shelf location must be less than 200 characters')
    .trim(),
  is_enabled: z.boolean().default(true),
});

type ShelfFormData = z.infer<typeof ShelfEditSchema>;

function ShelfEditViewGeneral({ shelf }: any) {
  const qrDialog = useBoolean();
  const { updateShelf } = useShelvesData('shelves');

  const defaultValues = useMemo<ShelfFormData>(
    () => ({
      name: shelf?.name || '',
      location: shelf?.location || '',
      is_enabled: shelf?.is_enabled || true,
    }),
    [shelf]
  );

  const methods = useForm<ShelfFormData>({
    resolver: zodResolver(ShelfEditSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, isDirty },
  } = methods;

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const onSubmit = handleSubmit(async (data: ShelfFormData) => {
    try {
      const finalData = {
        id: shelf.id,
        name: data.name,
        location: data.location,
        is_enabled: data.is_enabled,
      };
      updateShelf(finalData.id, finalData);
      toast.success('Shelf updated successfully!');
      reset(finalData);
    } catch (error: any) {
      console.error('Error updating shelf:', error);
    }
  });

  const qrCodeValue = useMemo(
    () => `${CONFIG.site.basePath}${paths.public.shelves.books(shelf?.id ?? '')}`,
    [shelf]
  );

  return (
    <>
      <Form methods={methods} onSubmit={onSubmit}>
        <Paper elevation={3} variant="elevation" sx={{ p: 3, my: 5 }}>
          <Stack
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 3,
            }}
          >
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="subtitle2" fontWeight="fontWeightBold">
                Shelf Details
              </Typography>
              <IconButton onClick={qrDialog.onTrue} color="primary">
                <Iconify icon="solar:qr-code-bold" />
              </IconButton>
            </Box>

            <RHFTextField
              name="name"
              label="Shelf name"
              placeholder="Enter shelf name (e.g., Fiction Books, Reference Materials)"
              size="small"
              fullWidth
              helperText="A descriptive name to identify this shelf"
            />

            <RHFTextField
              name="location"
              label="Shelf Location"
              placeholder="Enter physical location (e.g., Room A - Wall 1, Section B)"
              size="small"
              fullWidth
              helperText="Physical location or position of this shelf"
            />

            <FormControl>
              <Typography variant="subtitle2" fontWeight="fontWeightBold" sx={{ mb: 1 }}>
                Shelf Status
              </Typography>
              <RHFSwitch
                name="is_enabled"
                label="Enable this shelf for use"
                helperText="When enabled, this shelf will be available for organizing items. Disabled shelves are hidden from selection."
              />
            </FormControl>
          </Stack>
        </Paper>

        <Stack justifyContent="end" flexDirection="row" gap={2}>
          <Button variant="outlined" color="inherit" onClick={() => reset()}>
            Cancel
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            color="primary"
            disabled={!isDirty}
            loading={isSubmitting}
          >
            Save Changes
          </LoadingButton>
        </Stack>
      </Form>

      {/* QR Code Dialog */}
      <Dialog
        open={qrDialog.value}
        onClose={qrDialog.onFalse}
        maxWidth="sm"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
          },
        }}
      >
        <DialogTitle>
          <Typography variant="h6" component="span" fontWeight="bold">
            Shelf QR Code
          </Typography>
        </DialogTitle>

        <IconButton
          aria-label="close dialog"
          onClick={qrDialog.onFalse}
          sx={{
            position: 'absolute',
            right: 12,
            top: 12,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <DialogContent sx={{ pt: 2 }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <Box
              sx={{
                p: 2,
                bgcolor: 'background.paper',
                borderRadius: 1,
                boxShadow: (theme) => theme.customShadows?.z8,
              }}
            >
              <QRCodeSVG
                value={qrCodeValue}
                size={256}
                level="H"
                includeMargin
                imageSettings={{
                  src: '/logo/logo-book.png', // TODO: Add your logo
                  height: 40,
                  width: 40,
                  excavate: true,
                }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary" align="center">
              Scan this QR code to access the shelf details
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button variant="outlined" color="inherit" onClick={qrDialog.onFalse}>
            Close
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              // TODO: Implement QR code download
              const svg = document.querySelector('svg');
              if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                img.onload = () => {
                  canvas.width = img.width;
                  canvas.height = img.height;
                  ctx?.drawImage(img, 0, 0);
                  const pngFile = canvas.toDataURL('image/png');
                  const downloadLink = document.createElement('a');
                  downloadLink.download = `shelf-${shelf.id}-qr.png`;
                  downloadLink.href = pngFile;
                  downloadLink.click();
                };
                img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
              }
            }}
          >
            Download QR Code
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default ShelfEditViewGeneral;
