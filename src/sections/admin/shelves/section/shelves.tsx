import type { IShelvesItem } from 'src/types/shelves';

import { toast } from 'src/components/snackbar';

import CategoryCard from '../shelf-card';
import ListHOC from '../shelf-card-list-wrapper-hoc';

type Props = {
  categories: IShelvesItem[];
  onUpdateShelf: (id: string, updates: Partial<IShelvesItem>) => Promise<void>;
  onDeleteShelf: (id: string) => Promise<void>;
};

export const ShelvesList = ({ categories, onUpdateShelf, onDeleteShelf }: Props) => {
  const handleToggleActive = async ({
    id,
    field,
    status,
  }: {
    id: string;
    field: string;
    status: boolean;
  }) => {
    try {
      await onUpdateShelf(id, { is_enabled: status });
      toast.success('Shelf updated successfully!');
    } catch (error) {
      const errMsg = error?.response?.data?.message || error?.message;
      toast.error(errMsg);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await onDeleteShelf(id);
    } catch (error) {
      const errMsg = error?.response?.data?.message || error?.message;
      toast.error(errMsg);
    }
  };

  return (
    <ListHOC>
      {categories.map((category) => (
        <CategoryCard
          key={category.id}
          category={category}
          handleToggleActive={handleToggleActive}
          onDelete={handleDelete}
        />
      ))}
    </ListHOC>
  );
};
