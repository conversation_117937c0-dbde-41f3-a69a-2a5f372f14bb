import type { IShelvesItem } from 'src/types/shelves';

import { toast } from 'sonner';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Link, Stack, Switch, Button, FormControlLabel } from '@mui/material';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

type Props = {
  category: IShelvesItem;
  handleToggleActive: ({
    id,
    field,
    status,
  }: {
    id: string;
    field: string;
    status: boolean;
  }) => void;
  onDelete: (id: string) => Promise<void>;
};

export default function ShelfCard({ category, handleToggleActive, onDelete }: Props) {
  const { id, name, is_enabled, total_book_count, location } = category;

  const confirm = useBoolean();
  const shareDialog = useBoolean();

  const handleDeleteCategory = async () => {
    confirm.onFalse();
    try {
      await onDelete(id!);
      toast.success('Shelf deleted successfully!');
    } catch (error) {
      console.log('🚀 ~ handleDeleteCategory ~ error:', error);
    }
  };

  const redirectUrl = paths.dashboard.shelves.detail(id);

  return (
    <>
      <Card
        sx={{
          padding: 3,
        }}
      >
        <Box gap={2} display="flex" justifyContent="space-between">
          {/* Left section */}
          <Box gap={2} display="flex" flex={1} minWidth={0}>
            <Avatar alt={name} sx={{ width: 48, height: 48 }}>
              {name.charAt(0).toUpperCase()}
            </Avatar>
            <Stack sx={{ minWidth: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
                <Typography
                  sx={{
                    textTransform: 'capitalize',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    minWidth: 0,
                  }}
                  fontWeight="bold"
                  variant="subtitle1"
                >
                  <Link color="inherit" underline="hover" component={RouterLink} href={redirectUrl}>
                    {name}
                  </Link>
                </Typography>
              </Box>
              <Box
                display="flex"
                gap="4px"
                alignItems="center"
                sx={{ color: (theme) => theme.palette.grey[500] }}
              >
                <Iconify width="16" height="16" icon="solar:notes-bold" />
                <Typography variant="body1" fontSize="14px">
                  {total_book_count || 0} books
                </Typography>
              </Box>

              {/* Location */}
              {location && (
                <Box
                  display="flex"
                  gap="4px"
                  alignItems="center"
                  sx={{
                    color: (theme) => theme.palette.grey[500],
                  }}
                >
                  <Iconify width="16" height="16" icon="solar:map-point-bold" />
                  <Typography variant="body1" fontSize="14px">
                    {location}
                  </Typography>
                </Box>
              )}
            </Stack>
          </Box>

          {/* Right section */}
          <Box display="flex" alignItems="center" gap={1} flexShrink={0}>
            <IconButton onClick={shareDialog.onTrue}>
              <Iconify icon="solar:share-bold" />
            </IconButton>
            <IconButton onClick={confirm.onTrue} sx={{ color: 'error.dark' }}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Box>
        </Box>

        <Divider sx={{ my: 2, mx: -3, borderStyle: 'dashed' }} />

        <FormControlLabel
          control={
            <Switch
              checked={is_enabled}
              onChange={(e) => {
                handleToggleActive({ id: id!, field: 'is_enabled', status: e.target.checked });
              }}
              color="success"
            />
          }
          sx={{
            userSelect: 'none',
          }}
          label="Enable"
        />
      </Card>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={
          <Box display="flex" gap={2} alignItems="center">
            <Iconify icon="solar:trash-bin-trash-bold" />{' '}
            <Typography variant="h6" fontWeight="bold">
              Delete Shelf
            </Typography>
          </Box>
        }
        content={
          <>
            <Typography variant="subtitle1" fontWeight="bold" color="grey">
              Are you sure you want to delete `&quot;`{name} `&quot;`?
            </Typography>
            <Typography variant="body1" fontSize="16px" color="grey">
              All books under this shelf will also be hidden. This action cannot be undone.
            </Typography>
          </>
        }
        action={
          <Button variant="contained" color="error" onClick={handleDeleteCategory}>
            Delete
          </Button>
        }
      />
    </>
  );
}
