import type { IShelvesItem } from 'src/types/shelves';
import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import { z } from 'zod';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { LoadingButton } from '@mui/lab';
import {
  Button,
  Dialog,
  Typography,
  IconButton,
  FormControl,
  DialogTitle,
  DialogActions,
  DialogContent,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { RHFTextField } from 'src/components/hook-form';
import { Form } from 'src/components/hook-form/form-provider';
import { RHFSwitch } from 'src/components/hook-form/rhf-switch';

// Zod Schema
const ShelfCreationSchema = z.object({
  name: z
    .string()
    .min(1, 'Shelf name is required')
    .max(100, 'Shelf name must be less than 100 characters')
    .trim(),
  location: z
    .string()
    .min(1, 'Shelf location is required')
    .max(200, 'Shelf location must be less than 200 characters')
    .trim(),
  is_enabled: z.boolean().default(true),
});

type ShelfFormData = z.infer<typeof ShelfCreationSchema>;

interface ShelfNewDialogProps {
  dialog: IUseBooleanReturn;
  onCreateShelf: (shelfData: Omit<IShelvesItem, 'id'>) => Promise<void>;
}

export default function ShelfNewDialog({ dialog, onCreateShelf }: ShelfNewDialogProps) {
  const defaultValues: ShelfFormData = {
    name: '',
    location: '',
    is_enabled: true,
  };

  const methods = useForm<ShelfFormData>({
    resolver: zodResolver(ShelfCreationSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
    reset,
  } = methods;

  const onSubmit = handleSubmit(async (data: ShelfFormData) => {
    try {
      const shelfData: Omit<IShelvesItem, 'id'> = {
        name: data.name,
        location: data.location,
        is_enabled: data.is_enabled,
      };
      await onCreateShelf(shelfData);
      toast.success('Shelf created successfully!');
      handleCloseDialog();
    } catch (error: any) {
      console.log('🚀 ~ onSubmit ~ error:', error);
    }
  });

  const handleCloseDialog = () => {
    reset();
    dialog.onFalse();
  };

  return (
    <Dialog
      open={dialog.value}
      onClose={handleCloseDialog}
      fullWidth
      maxWidth="sm"
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 2,
        },
      }}
    >
      <Form methods={methods} onSubmit={onSubmit}>
        <DialogTitle>
          <Typography variant="h6" component="span" fontWeight="bold">
            Create New Shelf
          </Typography>
        </DialogTitle>

        <IconButton
          aria-label="close dialog"
          onClick={handleCloseDialog}
          sx={{
            position: 'absolute',
            right: 12,
            top: 12,
            color: (theme) => theme.palette.grey[500],
            '&:hover': {
              color: (theme) => theme.palette.grey[700],
            },
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            pt: 2,
          }}
        >
          {/* Shelf Name Field */}
          <RHFTextField
            name="name"
            label="Shelf Name"
            placeholder="Enter shelf name (e.g., Fiction Books, Reference Materials)"
            size="small"
            fullWidth
            helperText="A descriptive name to identify this shelf"
          />

          {/* Shelf Location Field */}
          <RHFTextField
            name="location"
            label="Shelf Location"
            placeholder="Enter physical location (e.g., Room A - Wall 1, Section B)"
            size="small"
            fullWidth
            helperText="Physical location or position of this shelf"
          />

          {/* Status Switch */}
          <FormControl>
            <Typography variant="subtitle2" fontWeight="fontWeightBold" sx={{ mb: 1 }}>
              Shelf Status
            </Typography>
            <RHFSwitch
              name="is_enabled"
              label="Enable this shelf for use"
              helperText="When enabled, this shelf will be available for organizing items. Disabled shelves are hidden from selection."
            />
          </FormControl>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            color="inherit"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            color="primary"
            loading={isSubmitting}
            loadingPosition="start"
            startIcon={<Iconify icon="mingcute:add-line" />}
          >
            Create Shelf
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}
