import { z as zod } from 'zod';
import { useState } from 'react';

import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';

import { signInWithGoogle } from 'src/auth/context/supabase';

export type SignInSchemaType = zod.infer<typeof SignInSchema>;

export const SignInSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
  password: zod
    .string()
    .min(1, { message: 'Password is required!' })
    .min(6, { message: 'Password must be at least 6 characters!' }),
});

// ----------------------------------------------------------------------

export function SupabaseSignInView() {
  const [errorMsg, setErrorMsg] = useState('');
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  // Google sign-in
  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleLoading(true);
      setErrorMsg('');
      await signInWithGoogle();
    } catch (error) {
      console.error('Google sign-in error:', error);
      setErrorMsg(error instanceof Error ? error.message : 'Google sign-in failed');
      setIsGoogleLoading(false);
    }
  };

  return (
    <>
      <Stack spacing={1.5} sx={{ mb: 5 }}>
        <Typography variant="h5">Sign in to your account</Typography>
      </Stack>

      {!!errorMsg && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMsg}
        </Alert>
      )}
      <LoadingButton
        fullWidth
        color="inherit"
        size="large"
        type="button"
        variant="contained"
        onClick={handleGoogleSignIn}
        loading={isGoogleLoading}
        loadingIndicator="Signing in with Google..."
        sx={{ mb: 2 }}
        startIcon={
          !isGoogleLoading && (
            <img
              src="https://developers.google.com/identity/images/g-logo.png"
              alt="Google"
              style={{ width: 20, height: 20 }}
            />
          )
        }
      >
        Sign in with Google
      </LoadingButton>
    </>
  );
}
