import { useState } from 'react';

import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import { Box, Chip, Stack, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { DonateBy } from 'src/sections/main/shelf/books/single-book/donated-by';

import TakeHome from './take-home';

const SingleBook = () => {
  const [open, setOpen] = useState(false);

  return (
    <Stack>
      <Box gap={2} display="flex" flex={1} minWidth={0}>
        <Avatar alt="Book" sx={{ width: 48, height: 48 }}>
          <Iconify icon="mdi:book-open-variant-outline" width={24} />
        </Avatar>
        <Stack sx={{ minWidth: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
            <Typography
              sx={{
                textTransform: 'capitalize',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                minWidth: 0,
              }}
              fontWeight="bold"
              variant="subtitle1"
            >
              {/* {item.name} */}
              Book Name
            </Typography>
          </Box>
          <Box
            display="flex"
            gap="4px"
            alignItems="center"
            sx={{ color: (theme) => theme.palette.grey[500] }}
          >
            <Iconify width="16" height="16" icon="mdi:account" />
            <Typography
              sx={{
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              variant="body1"
              fontSize="14px"
            >
              {/* {item.author} */}
              Author Name
            </Typography>
          </Box>
        </Stack>
      </Box>
      <Chip
        label="fiction"
        size="small"
        variant="outlined"
        sx={{
          width: 'min-content',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          mt: 2,
        }}
      />
      <Typography variant="caption" sx={{ mt: 1, fontSize: '14px', textAlign: 'start' }}>
        Lorem ipsum dolor sit amet consectetur. Commodo tellus vulputate suspendisse vel in orci
        ultrices augue. Eget fringilla eget vitae nunc tincidunt congue posuere interdum ultricies.
        Pharetra vitae enim blandit felis ante. Mattis proin odio lacus arcu maecenas. Lorem ipsum
        dolor sit amet consectetur. Commodo tellus vulputate suspendisse vel in orci ultrices augue.
        Eget fringilla eget vitae nunc tincidunt congue posuere interdum ultricies. Pharetra vitae
        enim blandit felis ante. Mattis proin odio lacus arcu maecenas.
      </Typography>
      <Box gap={2} display="flex" flex={1} sx={{ mt: 2 }}>
        <Button fullWidth variant="soft" size="large" color="error">
          Read Now
        </Button>
        <Button onClick={() => setOpen(true)} fullWidth variant="soft" size="large" color="error">
          Take Home
        </Button>
      </Box>
      <DonateBy name="John Doe" />
      <TakeHome open={open} onClose={() => setOpen(false)} />
    </Stack>
  );
};

export default SingleBook;
