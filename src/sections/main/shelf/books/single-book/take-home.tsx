import type { BoxProps } from '@mui/material';

import Avatar from '@mui/material/Avatar';
import {
  Box,
  Card,
  Stack,
  Dialog,
  useTheme,
  IconButton,
  Typography,
  dialogClasses,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

type Props = {
  open: boolean;
  onClose: () => void;
};

const TakeHome = ({ open, onClose, sx }: Props & BoxProps) => {
  const theme = useTheme();

  const handleClose = () => {
    onClose();
  };

  // Sample book data - you can replace with your actual data
  const books = [
    { id: 1, name: 'The Midnight Library', author: '<PERSON>' },
    { id: 2, name: 'Atomic Habits', author: '<PERSON>' },
    { id: 3, name: 'The Psychology of Money', author: '<PERSON>' },
    { id: 4, name: 'Educated', author: '<PERSON> Westover' },
    { id: 5, name: 'The Silent Patient', author: '<PERSON>' },
    { id: 6, name: 'Where the Crawdads Sing', author: '<PERSON><PERSON>' },
    { id: 7, name: 'The <PERSON> Husbands of <PERSON>', author: '<PERSON>' },
    { id: 8, name: '<PERSON><PERSON>', author: '<PERSON> Obama' },
  ];

  return (
    <Dialog
      open={open}
      fullWidth
      maxWidth="xs"
      onClose={handleClose}
      aria-labelledby="book-exchange-modal"
      aria-describedby="select-book-to-exchange"
      sx={{
        [`& .${dialogClasses.container}`]: { alignItems: 'center' },
      }}
    >
      <Box
        sx={{
          maxHeight: '90vh',

          borderRadius: 3,
          bgcolor: theme.palette.background.default,
          outline: 'none',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Fixed Header Section */}
        <Box sx={{ p: 3, pb: 0, flexShrink: 0 }}>
          <Stack
            direction="row"
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              mb: 1,
            }}
          >
            <Typography
              sx={{
                fontWeight: 600,
                fontSize: '18px',
                color: theme.palette.text.primary,
              }}
            >
              Select Book to Exchange
            </Typography>

            <IconButton
              aria-label="close dialog"
              onClick={handleClose}
              sx={{
                color: theme.palette.text.secondary,
                padding: '4px',
                '&:hover': {
                  color: theme.palette.text.primary,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <Iconify icon="mingcute:close-line" width={20} />
            </IconButton>
          </Stack>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              fontSize: '14px',
              lineHeight: 1.5,
              mb: 3,
            }}
          >
            Choose one of your books to place on the shelf
          </Typography>

          <Typography
            variant="body1"
            sx={{
              fontSize: '16px',
              fontWeight: 500,
              color: theme.palette.text.primary,
              mb: 2,
            }}
          >
            Your Books
            <Box
              component="span"
              sx={{
                bgcolor: theme.palette.background.neutral,
                borderRadius: '4px',
                px: 0.5,
                ml: 1,
              }}
            >
              {books.length}
            </Box>
          </Typography>
        </Box>

        {/* Scrollable Content Area */}
        <Scrollbar>
          <Box sx={{ flex: 1, overflow: 'hidden', px: 3 }}>
            <Stack spacing={2} sx={{ pb: 2 }}>
              {books.map((book) => (
                <Card
                  key={book.id}
                  sx={{
                    p: 3,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 3,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-1px)',
                      bgcolor: theme.palette.action.selected,
                    },
                    ...sx,
                  }}
                >
                  <Avatar alt="Book" sx={{ width: 48, height: 48 }}>
                    <Iconify icon="mdi:book-open-variant-outline" width={24} />
                  </Avatar>

                  <Stack sx={{ flex: 1, minWidth: 0 }}>
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '15px',
                        color: theme.palette.text.primary,
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                      }}
                    >
                      {book.name}
                    </Typography>

                    <Box display="flex" alignItems="center" gap={0.5} sx={{ mt: 0.5 }}>
                      <Iconify
                        width="14"
                        height="14"
                        icon="mdi:account"
                        sx={{ color: theme.palette.text.secondary }}
                      />
                      <Typography
                        sx={{
                          fontSize: '13px',
                          color: theme.palette.text.secondary,
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                        }}
                      >
                        {book.author}
                      </Typography>
                    </Box>
                  </Stack>

                  <Iconify
                    icon="mdi:chevron-right"
                    width={20}
                    sx={{ color: theme.palette.text.secondary }}
                  />
                </Card>
              ))}
            </Stack>
          </Box>
        </Scrollbar>

        {/* Fixed Bottom Section - Add New Book */}
        <Box
          sx={{
            flexShrink: 0,
            p: 3,
            pt: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
              p: 3,
              borderRadius: 2,
              cursor: 'pointer',
              backgroundColor: theme.palette.background.paper,
              transition: 'all 0.2s ease-in-out',
              border: `2px dashed ${theme.palette.divider}`,
              '&:hover': {
                borderColor: theme.palette.primary.main,
                bgcolor: theme.palette.action.hover,
              },
            }}
          >
            <Box
              sx={{
                width: 30,
                height: 30,
                borderRadius: '50%',
                // backgroundColor: theme.palette.background.neutral,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Iconify icon="mdi:plus" width={10} sx={{ color: theme.palette.text.secondary }} />
            </Box>

            <Typography
              sx={{
                fontWeight: 500,
                fontSize: '14px',
                color: theme.palette.text.primary,
                textAlign: 'center',
              }}
            >
              Add New Book
            </Typography>
          </Box>
        </Box>
      </Box>
    </Dialog>
  );
};

export default TakeHome;
