// create some dummy data for the books

import { type IDateValue } from './common';

export type ITestsTableFilters = {
  categoryId: string;
  categoryName: string;
  status?: 'Published' | 'Unpublished' | '';
  isPublished?: boolean;
  searchQuery?: string;
  questionId?: string;
};

export type ITestsTableFilterValue =
  | ITestsTableFilters['categoryId']
  | ITestsTableFilters['status']
  | string;

export type ITestTableItem = {
  id: string;
  name: string;
  instruction: string;
  totalQuestions: number;
  testDuration: number;
  isPublished: boolean;
  isRandomized: boolean;
  upperThreshold: number | null;
  lowerThreshold: number | null;
  nextTestId: string | null;
  prevTestId: string | null;
  isPrimary: boolean;
  totalResults: number;
  category: {
    id: string;
    name: string;
    color: string;
    isPublished: boolean;
  } | null;
  levels: ITestLevel[] | null;
};

export type IBooksTableData = {
  id: string;
  name: string;
  author: string;
  category: {
    id: string;
    name: string;
    color: string;
    isPublished: boolean;
  } | null;
  shelf: {
    id: string;
    name: string;
    location: string;
  } | null;
  description: string;
  isAvailable: boolean;
  isActive: boolean;
};

export type IBookRequestTableData = {
  id: string;
  name: string;
  author: string;
  category: {
    id: string;
    name: string;
    color: string;
    isPublished: boolean;
  } | null;
  shelf: {
    id: string;
    name: string;
    location: string;
  } | null;
  description: string;
  requestedBy: {
    id: string;
    name: string;
    email: string;
  };
  requestedAt: string;
  status: 'pending' | 'approved' | 'rejected';
};

export type IBooksTableFilters = {
  searchKeyword: string;
  categoryId?: string;
  isAvailable?: boolean;
  isActive?: boolean;
};

export type IBookRequestTableFilters = {
  searchKeyword: string;
  status?: 'pending' | 'approved' | 'rejected';
  categoryId?: string;
};

export type IBookCard = {
  id: string;
  name: string;
  author: string;
  tags: string[];
  description: string;
  postedAt: IDateValue;
};

export const booksData: IBookCard[] = [
  {
    id: '1',
    name: '1984',
    author: 'George Orwell',
    tags: ['dystopian', 'political fiction', 'classic'],
    description: 'A chilling portrayal of a totalitarian society ruled by Big Brother.',
    postedAt: '2021-01-01',
  },
  {
    id: '2',
    name: 'To Kill a Mockingbird',
    author: 'Harper Lee',
    tags: ['classic', 'racism', 'coming-of-age'],
    description:
      'A novel about the serious issues of rape and racial inequality told through the eyes of a child.',
    postedAt: '2021-01-02',
  },
  {
    id: '3',
    name: 'Sapiens: A Brief History of Humankind',
    author: 'Yuval Noah Harari',
    tags: ['history', 'non-fiction', 'evolution'],
    description:
      'Explores the journey of Homo sapiens from ancient ancestors to modern civilization.',
    postedAt: '2021-01-03',
  },
  {
    id: '4',
    name: 'The Subtle Art of Not Giving a F*ck',
    author: 'Mark Manson',
    tags: ['self-help', 'real talk', 'psychology'],
    description: 'A brutally honest self-help book that shatters the positivity myth.',
    postedAt: '2021-01-04',
  },
  {
    id: '5',
    name: 'Pride and Prejudice',
    author: 'Jane Austen',
    tags: ['romance', 'classic', 'society'],
    description: 'A witty commentary on love and marriage in 19th-century England.',
    postedAt: '2021-01-05',
  },
  {
    id: '6',
    name: 'The 5 AM Club: Own Your Morning. Elevate Your Life.',
    author: 'Robin Sharma',
    tags: ['motivation', 'habits', 'success'],
    description: 'Discover how waking up early can transform your life.',
    postedAt: '2021-01-06',
  },
  {
    id: '7',
    name: 'Harry Potter and the Sorcerer’s Stone',
    author: 'J.K. Rowling',
    tags: ['fantasy', 'magic', 'young adult'],
    description: 'A young boy discovers he’s a wizard and enters a magical world.',
    postedAt: '2021-01-07',
  },
  {
    id: '8',
    name: 'Thinking, Fast and Slow',
    author: 'Daniel Kahneman',
    tags: ['psychology', 'cognitive science', 'decision making'],
    description: 'Reveals the two systems of thinking that drive our decisions.',
    postedAt: '2021-01-08',
  },
  {
    id: '9',
    name: 'A Short History of Nearly Everything',
    author: 'Bill Bryson',
    tags: ['science', 'humor', 'history'],
    description: 'Takes a humorous look at how we understand the universe.',
    postedAt: '2021-01-09',
  },
  {
    id: '10',
    name: 'Educated: A Memoir',
    author: 'Tara Westover',
    tags: ['memoir', 'inspiration', 'family'],
    description: 'A woman escapes her strict upbringing and earns a PhD from Cambridge.',
    postedAt: '2021-01-10',
  },
  {
    id: '11',
    name: 'The Great Gatsby',
    author: 'F. Scott Fitzgerald',
    tags: ['classic', 'jazz age', 'American dream'],
    description: 'A tragic story of love and ambition set in the Roaring Twenties.',
    postedAt: '2021-01-11',
  },
  {
    id: '12',
    name: 'The Midnight Library',
    author: 'Matt Haig',
    tags: ['fantasy', 'life choices', 'mental health'],
    description: 'A library between life and death holds endless possibilities.',
    postedAt: '2021-01-12',
  },
  {
    id: '13',
    name: 'The Man Who Mistook His Wife for a Hat',
    author: 'Oliver Sacks',
    tags: ['neurology', 'case studies', 'medicine'],
    description: 'Strange but real neurological tales told with compassion.',
    postedAt: '2021-01-13',
  },
  {
    id: '14',
    name: 'Atomic Habits: An Easy & Proven Way to Build Good Habits & Break Bad Ones',
    author: 'James Clear',
    tags: ['productivity', 'self-improvement', 'habits'],
    description: 'Tiny changes lead to remarkable results.',
    postedAt: '2021-01-14',
  },
  {
    id: '15',
    name: 'The Hitchhiker’s Guide to the Galaxy',
    author: 'Douglas Adams',
    tags: [
      'sci-fi',
      'comedy',
      'adventure',
      'absurd',
      'intergalactic-travel',
      'cult-classic',
      'space-opera',
    ],
    description:
      'A hilarious journey through space with a towel and a very bad sense of direction.',
    postedAt: '2021-01-15',
  },
];

// create some mock data for the books table
export const booksTableData: IBooksTableData[] = [
  {
    id: '1',
    name: '1984',
    author: 'George Orwell',
    category: { id: '1', name: 'Fiction', color: '#E00034', isPublished: true },
    shelf: { id: '1', name: 'Fiction Shelf A', location: 'Floor 1, Section A' },
    description: 'A dystopian social science fiction novel and cautionary tale.',
    isAvailable: true,
    isActive: true,
  },
  {
    id: '2',
    name: 'To Kill a Mockingbird',
    author: 'Harper Lee',
    category: { id: '1', name: 'Fiction', color: '#E00034', isPublished: true },
    shelf: { id: '1', name: 'Fiction Shelf A', location: 'Floor 1, Section A' },
    description: 'A novel about racial injustice and childhood in the American South.',
    isAvailable: false,
    isActive: true,
  },
  {
    id: '3',
    name: 'Sapiens',
    author: 'Yuval Noah Harari',
    category: { id: '2', name: 'Non-Fiction', color: '#FFB000', isPublished: true },
    shelf: { id: '2', name: 'Non-Fiction Shelf B', location: 'Floor 1, Section B' },
    description: 'A brief history of humankind.',
    isAvailable: true,
    isActive: false,
  },
];

// create some mock data for book requests
export const bookRequestsTableData: IBookRequestTableData[] = [
  {
    id: '1',
    name: 'The Great Gatsby',
    author: 'F. Scott Fitzgerald',
    category: { id: '1', name: 'Fiction', color: '#E00034', isPublished: true },
    shelf: { id: '1', name: 'Fiction Shelf A', location: 'Floor 1, Section A' },
    description: 'A classic American novel set in the Jazz Age.',
    requestedBy: { id: '1', name: 'John Doe', email: '<EMAIL>' },
    requestedAt: '2024-01-15T10:30:00Z',
    status: 'pending',
  },
  {
    id: '2',
    name: 'Atomic Habits',
    author: 'James Clear',
    category: { id: '3', name: 'Self-Help', color: '#00B2FF', isPublished: true },
    shelf: { id: '3', name: 'Self-Help Shelf C', location: 'Floor 2, Section A' },
    description: 'An easy and proven way to build good habits and break bad ones.',
    requestedBy: { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
    requestedAt: '2024-01-14T14:20:00Z',
    status: 'approved',
  },
];

export interface ITestLevel {
  id: string;
  name: string;
  minPercentage: number;
}

export interface ICreateOrUpdateTest {
  id: string;
  name: string;
  instruction: string;
  isRandomized: boolean;
  dimension: number;
  totalQuestions: number;
  testDuration: number;
  isPublished: boolean;
  isArchived: boolean;
  levels: Partial<ITestLevel>[];
}

type IUserPremise = {
  premise: {
    name: string;
  };
};

type IUser = {
  id: string;
  name: string;
  surname: string;
  email: string;
  premises: IUserPremise[];
};

type ITestResult = {
  id: string;
  createdAt: Date;
  studentPoints: number;
  totalPoints: number;
  student: IUser;
  testLevels: ITestLevel;
  nextTestId: string;
};

export type ITestSection = {
  id: string;
  name: string;
  instruction: string;
  position: number;
  _tempId?: string;
};

export type ITestQuestion = {
  id: string;
  _tempId?: string;
  questionId: string;
  sectionId: string;
  testId: string;
  question: string;
  answers: Array<{ text: string; points: number }>;
  totalPoints: number;
  instruction?: string;
  imagePath?: string;
  audioPath?: string;
  videoPath?: string;
  mimeType?: string;
  isExternal: boolean;
  questionType: 'text' | 'single' | 'multiple';
  tags: string[];
  position: number;
  isUpdatedOnQuestionsTable: boolean;
  isPublished: boolean;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  isNewQuestion?: boolean;
  questionNumber: number;
};

type ITestCreatedBy = {
  name: string;
  surname: string;
};

export type ITestDetailsResponse = {
  id: string;
  name: string;
  instruction: string;
  isRandomized: boolean;
  dimension?: number;
  createdBy: ITestCreatedBy;
  categoryId: string;
  totalQuestions: number;
  totalResults: number;
  testDuration: number;
  upperThreshold: number | null;
  lowerThreshold: number | null;
  nextTestId: string | null;
  prevTestId: string | null;
  isPrimary: boolean;
  isPublished: boolean;
  isArchived: boolean;
  isRepeatable: boolean;
  createdAt: string;
  updatedAt: string;
  levels: ITestLevel[];
  results: ITestResult[];
  testQuestions: ITestQuestion[];
  sections: ITestSection[];
  category: {
    isPublished: boolean;
    name: string;
  };
};

// const newQuestionYupSchema = Yup.object({
//   question: Yup.string().required('Question cannot be empty').min(1),
//   answers: Yup.array().of(
//     Yup.object({
//       text: Yup.string().required('Answer text cannot be empty').min(1),
//       points: Yup.number().required(),
//     })
//   ),
//   totalPoints: Yup.number().integer().positive('Total points must be a non-negative integer'),
//   instruction: Yup.string().nullable(),
//   imagePath: Yup.string().nullable(),
//   audioPath: Yup.string().nullable(),
//   videoPath: Yup.string().nullable(),
//   mimeType: Yup.string().nullable(),
//   questionType: Yup.string().oneOf(['single', 'multiple', 'text']).required(),
//   tags: Yup.array().of(Yup.string()).default([]),
//   isPublished: Yup.boolean().default(true),
// });

// const saveTestQuestionsYupSchema = Yup.array().of(
//   newQuestionYupSchema.shape({
//     id: Yup.string().uuid(),
//     sectionId: Yup.string().uuid().nullable(),
//     testId: Yup.string().uuid().required(),
//     position: Yup.number().required(),
//     isUpdatedOnQuestionsTable: Yup.boolean().default(false),
//     questionId: Yup.string().uuid().nullable(),
//     _tempId: Yup.string().uuid().optional(),
//   })
// );

// const saveTestSectionsYupSchema = Yup.object({
//   id: Yup.string().uuid(),
//   name: Yup.string().required(),
//   instruction: Yup.string().default(''),
//   position: Yup.number().required(),
//   testId: Yup.string().uuid().required(),
//   questions: saveTestQuestionsYupSchema.default([]),
//   _tempId: Yup.string().uuid().optional(),
// });

// export const saveTestYupSchema = Yup.object({
//   independentQuestions: saveTestQuestionsYupSchema.default([]),
//   sections: Yup.array().of(saveTestSectionsYupSchema.default([])),
//   isRandomized: Yup.boolean().default(false),
//   dimension: Yup.number().nullable().default(null),
//   totalQuestions: Yup.number().default(0),
// });

// export type SaveTestSectionsYupSchema = Yup.InferType<typeof saveTestSectionsYupSchema>;

// export type SaveTestQuestionsYupSchema = Yup.InferType<typeof saveTestYupSchema>;

// export type ICreateOrUpdateTestData = Partial<ICreateOrUpdateTest>;
