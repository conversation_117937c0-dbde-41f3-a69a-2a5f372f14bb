import voca from 'voca';

type Valuable<T> = { [K in keyof T as T[K] extends null | undefined ? never : K]: T[K] };

export function removeFalsyValuesFromObject<
  // eslint-disable-next-line @typescript-eslint/ban-types
  T extends {},
  V = Valuable<T>,
>(obj: T): V {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([, v]) => !((typeof v === 'string' && !v.length) || v === null || typeof v === 'undefined')
    )
  ) as V;
}

export const convertToTitleCase = (text: string) => {
  const filteredText = text.replaceAll(/[_-]/g, ' ');
  return voca.titleCase(filteredText);
};
